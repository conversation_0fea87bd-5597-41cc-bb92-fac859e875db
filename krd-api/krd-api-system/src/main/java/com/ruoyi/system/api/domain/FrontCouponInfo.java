package com.ruoyi.system.api.domain;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName FrontCouponInfo
 * @TableName front_gategory
 * @Description 优惠券详情信息
 * <AUTHOR>
 * @Date 2025/5/22 上午11:23
 */
@Data
@Schema(description = "优惠券详情信息")
@TableName("front_coupon_info")
public class FrontCouponInfo implements Serializable {
    @Schema(name="ID")
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    @Schema(name="领取方式 0-主动领取 1-好友赠送 2-购物抵扣 3-已过期")
    private String type;
    @Schema(name="领取用户")
    private Long userId;
    @Schema(name="创建时间")
    private LocalDateTime createTime;
    @Schema(name="0-待使用 1-已使用 2-已过期")
    private String status;
    @Schema(name="使用/过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime useTime;
    @Schema(name="订单编号")
    private String orderNumber;
    @Schema(name="")
    private Date updateTime;
    @Schema(name="")
    private String createBy;
    @Schema(name="")
    private String updateBy;
    @Schema(name="优惠券id")
    private Long couponId;
    @Schema(name="券金额")
    private BigDecimal balance;
    @Schema(name="优惠券排序(用于区分使用那张优惠券)")
    private Integer sort;

    @Schema(name="优惠券名称")
    @TableField(exist = false)
    private String name;
    @Schema(name = "用户电话")
    @TableField(exist = false)
    private String phone;
}
