<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontCouponMapper">
    
    <resultMap type="com.ruoyi.system.api.domain.StoreFrontCoupon" id="FrontCouponResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="type"    column="type"    />
        <result property="total"    column="total"    />
        <result property="limits"    column="limits"    />
        <result property="balance"    column="balance"    />
        <result property="threshold"    column="threshold"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="used"    column="used"    />
        <result property="remark"    column="remark"    />
        <result property="isStatus"    column="is_status"    />
        <result property="isDel"    column="is_del"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="usedType"    column="used_type"    />
    </resultMap>

    <sql id="selectFrontCouponVo">
        select id, title, type, total, limits, balance, threshold, expiration_date, used, remark, is_status, is_del, create_time, update_time, create_by, update_by,used, used_type from front_coupon
    </sql>

    <select id="selectFrontCouponList" resultType="com.ruoyi.system.api.domain.vo.FrontCouponVo$FrontCouponList">
        SELECT
            c.id couponId,
            c.title title,
            c.type type,
            c.used_type used,
            c.threshold threshold,
            c.balance balance,
            c.total total,
            CASE
            WHEN c.total = 0 THEN COALESCE(i.info_total, 0)
            ELSE c.total - COALESCE(i.info_total, 0)
            END AS `limit`,
            COALESCE(i.info_status, 0) AS quantity,
            c.expiration_date expirationDate,
            c.is_status isStatus,
            c.create_time
        FROM
            front_coupon c
        LEFT JOIN (
        SELECT
        coupon_id,
        COUNT(*) AS info_total,
        SUM(`status` = 1) AS info_status
        FROM front_coupon_info
        GROUP BY coupon_id
        ) i ON c.id = i.coupon_id
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                c.title LIKE concat('%', #{query.keyword}, '%')
                OR c.id LIKE concat('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.type != null">
                AND c.type = #{query.type}
            </if>
        </where>
    </select>
    
    <select id="selectFrontCouponById" parameterType="Long" resultMap="FrontCouponResult">
        <include refid="selectFrontCouponVo"/>
        where id = #{id}
    </select>

    <select id="selectFrontCouponDetailById" resultType="com.ruoyi.system.api.domain.vo.FrontCouponVo$FrontCouponDetail">
        SELECT
            c.id couponId,
            c.title title,
            c.type type,
            c.used_type used,
            c.threshold threshold,
            c.balance balance,
            c.total total,
            CASE
                WHEN c.total = 0 THEN
                    COALESCE(i.info_total, 0)
                ELSE
                    c.total - COALESCE(i.info_total, 0)
                END AS `limit`,
            COALESCE(i.info_status1, 0) AS usedQuantity,
            COALESCE(i.info_total, 0) AS quantity,
            COALESCE(i.info_status0, 0) AS toBeUsedQuantity,
            COALESCE(i.info_status2, 0) AS expiredQuantity,
            c.expiration_date expirationDate,
            c.create_time
        FROM
            front_coupon c
                LEFT JOIN (
                SELECT
                    coupon_id,
                    COUNT(*) AS info_total,
                    SUM(`status` = 1) AS info_status1,
                    SUM(`status` = 0) AS info_status0,
                    SUM(`status` = 2) AS info_status2
                FROM
                    front_coupon_info
                GROUP BY
                    coupon_id
            ) i ON c.id = i.coupon_id
        WHERE c.id = #{id}
    </select>

    <insert id="insertFrontCoupon" parameterType="com.ruoyi.system.api.domain.StoreFrontCoupon">
        insert into front_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="type != null">type,</if>
            <if test="total != null">total,</if>
            <if test="limits != null">limits,</if>
            <if test="balance != null">balance,</if>
            <if test="threshold != null">threshold,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="used != null">used,</if>
            <if test="remark != null">remark,</if>
            <if test="isStatus != null">is_status,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="total != null">#{total},</if>
            <if test="limits != null">#{limits},</if>
            <if test="balance != null">#{balance},</if>
            <if test="threshold != null">#{threshold},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="used != null">#{used},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isStatus != null">#{isStatus},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFrontCoupon" parameterType="com.ruoyi.system.api.domain.StoreFrontCoupon">
        update front_coupon
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="total != null">total = #{total},</if>
            <if test="limits != null">limits = #{limits},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="threshold != null">threshold = #{threshold},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="used != null">used = #{used},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isStatus != null">is_status = #{isStatus},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrontCouponById" parameterType="Long">
        delete from front_coupon where id = #{id}
    </delete>

    <delete id="deleteFrontCouponByIds" parameterType="String">
        delete from front_coupon where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>