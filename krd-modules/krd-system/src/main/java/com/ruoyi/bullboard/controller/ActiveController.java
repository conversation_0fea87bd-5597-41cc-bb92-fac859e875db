package com.ruoyi.bullboard.controller;

import com.ruoyi.bullboard.service.ActiveService;
import com.ruoyi.bullboard.vo.*;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 活动看板统计Controller
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 活动看板统计控制器
 */
@Tag(name = "活动看板统计", description = "活动看板统计相关接口")
@RestController
@RequestMapping("/active")
public class ActiveController extends BaseController {

    @Autowired
    private ActiveService activeService;

    /**
     * 获取优惠券统计信息
     */
    @Operation(summary = "获取优惠券统计信息", description = "获取优惠券的总发行量、已领取数量、待使用数量、已使用数量、使用率、领取率等统计信息")
    @GetMapping("/coupon/statistics")
    public AjaxResult getCouponStatistics() {
        CouponStatisticsVO statistics = activeService.getCouponStatistics();
        return AjaxResult.success(statistics);
    }

    /**
     * 获取优惠券折线图数据
     */
    @Operation(summary = "获取优惠券折线图数据", description = "根据时间范围获取优惠券每日统计数据，用于绘制折线图")
    @GetMapping("/coupon/line")
    public AjaxResult getCouponLine(
            @Parameter(description = "开始日期", example = "2025-06-01") @RequestParam String startDate,
            @Parameter(description = "结束日期", example = "2025-06-30") @RequestParam String endDate) {
        List<CouponLineVo> lineData = activeService.getCouponLine(startDate, endDate);
        return AjaxResult.success(lineData);
    }

    /**
     * 获取礼品卡统计信息
     */
    @Operation(summary = "获取礼品卡统计信息", description = "获取礼品卡的累计销售金额、待使用金额、已使用金额、购买率、使用率等统计信息")
    @GetMapping("/gift/statistics")
    public AjaxResult getGiftStatistics() {
        GiftStatisticsVO statistics = activeService.getGiftStatistics();
        return AjaxResult.success(statistics);
    }

    /**
     * 获取礼品卡折线图数据
     */
    @Operation(summary = "获取礼品卡折线图数据", description = "根据时间范围获取礼品卡每日统计数据，用于绘制折线图")
    @GetMapping("/gift/line")
    public AjaxResult getGiftLine(
            @Parameter(description = "开始日期", example = "2025-06-01") @RequestParam String startDate,
            @Parameter(description = "结束日期", example = "2025-06-30") @RequestParam String endDate) {
        List<GiftLineVo> lineData = activeService.getGiftLine(startDate, endDate);
        return AjaxResult.success(lineData);
    }

    /**
     * 获取积分统计信息
     */
    @Operation(summary = "获取积分统计信息", description = "获取积分的总积分、累计消费积分、累计奖励积分、累计问卷积分、使用率等统计信息")
    @GetMapping("/source/statistics")
    public AjaxResult getSourceStatistics() {
        SourceStatisticsVO statistics = activeService.getSourceStatistics();
        return AjaxResult.success(statistics);
    }

    /**
     * 获取积分折线图数据
     */
    @Operation(summary = "获取积分折线图数据", description = "根据时间范围获取积分每日统计数据，用于绘制折线图")
    @GetMapping("/source/line")
    public AjaxResult getSourceLine(
            @Parameter(description = "开始日期", example = "2025-06-01") @RequestParam String startDate,
            @Parameter(description = "结束日期", example = "2025-06-30") @RequestParam String endDate) {
        List<SourceLineVo> lineData = activeService.getSourceLine(startDate, endDate);
        return AjaxResult.success(lineData);
    }
}
