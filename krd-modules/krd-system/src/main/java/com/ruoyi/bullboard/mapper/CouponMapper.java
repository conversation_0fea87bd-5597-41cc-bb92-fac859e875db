package com.ruoyi.bullboard.mapper;

import com.ruoyi.bullboard.vo.CouponLineVo;
import com.ruoyi.bullboard.vo.CouponStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 优惠券统计相关查询
 */
@Mapper
public interface CouponMapper {

    /**
     * 获取优惠券统计信息
     * @return 优惠券统计信息
     */
    CouponStatisticsVO getCouponStatistics();

    /**
     * 获取优惠券折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 优惠券折线图数据列表，每个元素代表一天的数据
     */
    List<CouponLineVo> getCouponLine(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
