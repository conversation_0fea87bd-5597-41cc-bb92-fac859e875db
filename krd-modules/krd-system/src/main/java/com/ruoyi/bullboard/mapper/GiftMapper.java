package com.ruoyi.bullboard.mapper;

import com.ruoyi.bullboard.vo.GiftLineVo;
import com.ruoyi.bullboard.vo.GiftStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 礼品卡统计相关查询
 */
@Mapper
public interface GiftMapper {

    /**
     * 获取礼品卡统计信息
     * @return 礼品卡统计信息
     */
    GiftStatisticsVO getGiftStatistics();

    /**
     * 获取礼品卡折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 礼品卡折线图数据列表，每个元素代表一天的数据
     */
    List<GiftLineVo> getGiftLine(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
