package com.ruoyi.bullboard.mapper;

import com.ruoyi.bullboard.vo.SourceLineVo;
import com.ruoyi.bullboard.vo.SourceStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 积分统计相关查询
 */
@Mapper
public interface SourceMapper {

    /**
     * 获取积分统计信息
     * @return 积分统计信息
     */
    SourceStatisticsVO getSourceStatistics();

    /**
     * 获取积分折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 积分折线图数据列表，每个元素代表一天的数据
     */
    List<SourceLineVo> getSourceLine(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
