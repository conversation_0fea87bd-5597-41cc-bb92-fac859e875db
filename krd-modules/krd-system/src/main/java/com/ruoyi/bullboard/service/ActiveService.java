package com.ruoyi.bullboard.service;

import com.ruoyi.bullboard.vo.*;

import java.util.List;

/**
 * 活动看板统计Service接口
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 活动看板统计服务接口
 */
public interface ActiveService {

    /**
     * 获取优惠券统计信息
     * @return 优惠券统计信息
     */
    CouponStatisticsVO getCouponStatistics();

    /**
     * 获取优惠券折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 优惠券折线图数据列表
     */
    List<CouponLineVo> getCouponLine(String startDate, String endDate);

    /**
     * 获取礼品卡统计信息
     * @return 礼品卡统计信息
     */
    GiftStatisticsVO getGiftStatistics();

    /**
     * 获取礼品卡折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 礼品卡折线图数据列表
     */
    List<GiftLineVo> getGiftLine(String startDate, String endDate);

    /**
     * 获取积分统计信息
     * @return 积分统计信息
     */
    SourceStatisticsVO getSourceStatistics();

    /**
     * 获取积分折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 积分折线图数据列表
     */
    List<SourceLineVo> getSourceLine(String startDate, String endDate);
}
