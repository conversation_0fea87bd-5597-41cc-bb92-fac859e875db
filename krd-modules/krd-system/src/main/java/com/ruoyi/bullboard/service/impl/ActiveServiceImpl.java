package com.ruoyi.bullboard.service.impl;

import com.ruoyi.bullboard.mapper.CouponMapper;
import com.ruoyi.bullboard.mapper.GiftMapper;
import com.ruoyi.bullboard.mapper.SourceMapper;
import com.ruoyi.bullboard.service.ActiveService;
import com.ruoyi.bullboard.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 活动看板统计Service业务层处理
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 活动看板统计服务实现类
 */
@Service
public class ActiveServiceImpl implements ActiveService {

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private GiftMapper giftMapper;

    @Autowired
    private SourceMapper sourceMapper;

    /**
     * 获取优惠券统计信息
     * @return 优惠券统计信息
     */
    @Override
    public CouponStatisticsVO getCouponStatistics() {
        return couponMapper.getCouponStatistics();
    }

    /**
     * 获取优惠券折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 优惠券折线图数据列表
     */
    @Override
    public List<CouponLineVo> getCouponLine(String startDate, String endDate) {
        return couponMapper.getCouponLine(startDate, endDate);
    }

    /**
     * 获取礼品卡统计信息
     * @return 礼品卡统计信息
     */
    @Override
    public GiftStatisticsVO getGiftStatistics() {
        return giftMapper.getGiftStatistics();
    }

    /**
     * 获取礼品卡折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 礼品卡折线图数据列表
     */
    @Override
    public List<GiftLineVo> getGiftLine(String startDate, String endDate) {
        return giftMapper.getGiftLine(startDate, endDate);
    }

    /**
     * 获取积分统计信息
     * @return 积分统计信息
     */
    @Override
    public SourceStatisticsVO getSourceStatistics() {
        return sourceMapper.getSourceStatistics();
    }

    /**
     * 获取积分折线图数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     * @return 积分折线图数据列表
     */
    @Override
    public List<SourceLineVo> getSourceLine(String startDate, String endDate) {
        return sourceMapper.getSourceLine(startDate, endDate);
    }
}
