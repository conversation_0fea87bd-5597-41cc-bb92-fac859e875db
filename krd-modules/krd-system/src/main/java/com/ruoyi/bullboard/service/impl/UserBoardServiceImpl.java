package com.ruoyi.bullboard.service.impl;

import com.ruoyi.bullboard.service.UserBoardService;
import com.ruoyi.bullboard.vo.*;
import com.ruoyi.user.mapper.FrontUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 用户数据看板服务实现类
 * @Author: suhai
 * @Date: 2025/6/26
 * @Description: 用户数据看板相关业务逻辑实现
 */
@Slf4j
@Service
public class UserBoardServiceImpl implements UserBoardService {

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Resource(name = "ruoyi-thread-pool-uniuser-query")
    private ExecutorService executorService;

    @Override
    public UserBoardTopVo getUserBoardTop() {
        return frontUserMapper.getUserBoardTop();
    }

    @Override
    public List<UserDataLineVo> getUserDataLine(String startDate, String endDate) {
        return frontUserMapper.getUserDataLine(startDate, endDate);
    }

    @Override
    public UserAddNewInfo getUserAddNewInfo(String startDate, String endDate) {
        return frontUserMapper.getUserAddNewInfo(startDate, endDate);
    }

    @Override
    public UserAccountVo getUserAccount(Integer type) {
        return frontUserMapper.getUserAccount(type);
    }

    @Override
    public List<UserDataInfoVo> getUserDataInfo(String startDate, String endDate) {
        return frontUserMapper.getUserDataInfo(startDate, endDate);
    }

    @Override
    public List<UserAgeDistributionVo> getUserAgeDistribution(Integer type) {
        return frontUserMapper.getUserAgeDistribution(type);
    }

    @Override
    public List<UserRegionDistributionVo> getUserRegionDistribution(Integer type) {
        return frontUserMapper.getUserRegionDistribution(type);
    }

    @Override
    public List<UserTagDistributionVo> getUserTagDistribution() {
        return frontUserMapper.getUserTagDistribution();
    }

    @Override
    public UserBoardInitVo initUserBoardData(String startDate, String endDate) {
        UserBoardInitVo result = new UserBoardInitVo();

        try {
            // 并发执行所有查询
            Future<UserBoardTopVo> userBoardTopFuture = executorService.submit(() ->
                frontUserMapper.getUserBoardTop());

            Future<List<UserDataLineVo>> userDataLineFuture = executorService.submit(() ->
                frontUserMapper.getUserDataLine(startDate, endDate));

            Future<UserAddNewInfo> userAddNewInfoFuture = executorService.submit(() ->
                frontUserMapper.getUserAddNewInfo(startDate, endDate));

            Future<List<UserDataInfoVo>> userDataInfoFuture = executorService.submit(() ->
                frontUserMapper.getUserDataInfo(startDate, endDate));

            // 用户占比信息（三种类型）
            Future<UserAccountVo> newUserAccountFuture = executorService.submit(() ->
                frontUserMapper.getUserAccount(0));

            // 用户年龄分布（三种类型）
            Future<List<UserAgeDistributionVo>> newUserAgeFuture = executorService.submit(() ->
                frontUserMapper.getUserAgeDistribution(0));

            // 用户地域分布（三种类型）
            Future<List<UserRegionDistributionVo>> newUserRegionFuture = executorService.submit(() ->
                frontUserMapper.getUserRegionDistribution(0));

            // 用户标签分布
            Future<List<UserTagDistributionVo>> userTagFuture = executorService.submit(() ->
                frontUserMapper.getUserTagDistribution());

            // 获取所有结果
            result.setUserBoardTop(userBoardTopFuture.get());
            result.setUserDataLine(userDataLineFuture.get());
            result.setUserAddNewInfo(userAddNewInfoFuture.get());
            result.setUserDataInfo(userDataInfoFuture.get());

            // 设置用户占比信息
            result.setNewUserAccount(newUserAccountFuture.get());

            // 设置用户年龄分布
            result.setNewUserAgeDistribution(newUserAgeFuture.get());

            // 设置用户地域分布
            result.setNewUserRegionDistribution(newUserRegionFuture.get());


            // 设置用户标签分布
            result.setUserTagDistribution(userTagFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            log.error("并发获取用户数据看板信息异常", e);
        }
        return result;
    }
}
