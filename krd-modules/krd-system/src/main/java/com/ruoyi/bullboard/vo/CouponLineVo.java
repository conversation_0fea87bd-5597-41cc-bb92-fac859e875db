package com.ruoyi.bullboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠券统计信息
 */
@Data
@Schema(description = "优惠券统计信息")
public class CouponLineVo implements Serializable {

    //时间
    private String date;

    //待使用金额
    private BigDecimal pendingUse;

    //已使用金额
    private BigDecimal totalUsed;

    //已过期金额
    private BigDecimal expired;
}
