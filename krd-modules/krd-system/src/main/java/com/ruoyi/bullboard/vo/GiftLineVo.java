package com.ruoyi.bullboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 礼品卡折线图统计信息
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 礼品卡折线图VO
 */
@Data
@Schema(description = "礼品卡折线图统计信息")
public class GiftLineVo implements Serializable {

    //时间
    private String date;

    //累计销售金额
    private BigDecimal totalSales;

    //待使用金额 (status = 0)
    private BigDecimal pendingUse;

    //已使用金额 (status = 1)
    private BigDecimal totalUsed;
}
