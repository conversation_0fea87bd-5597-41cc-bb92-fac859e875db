package com.ruoyi.bullboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 礼品卡统计信息
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 礼品卡统计VO
 */
@Data
@Schema(description = "礼品卡统计信息")
public class GiftStatisticsVO implements Serializable {

    @Schema(description = "累计销售礼品卡金额（元）")
    private BigDecimal totalSales;

    @Schema(description = "待使用礼品卡金额（元）")
    private BigDecimal pendingUse;

    @Schema(description = "已使用礼品卡金额（元）")
    private BigDecimal totalUsed;

    @Schema(description = "礼品卡购买率（%）")
    private BigDecimal purchaseRate;

    @Schema(description = "礼品卡使用率（%）")
    private BigDecimal usageRate;
}
