package com.ruoyi.bullboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分折线图统计信息
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 积分折线图VO
 */
@Data
@Schema(description = "积分折线图统计信息")
public class SourceLineVo implements Serializable {

    //时间
    private String date;

    //累计活动发放积分
    private Integer totalActivityPoints;

    //累计签到积分
    private Integer totalSignPoints;

    //累计问卷积分
    private Integer totalSurveyPoints;
}
