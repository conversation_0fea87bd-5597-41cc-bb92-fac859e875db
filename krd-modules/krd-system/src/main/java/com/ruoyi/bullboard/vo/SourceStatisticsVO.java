package com.ruoyi.bullboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 积分统计信息
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 积分统计VO
 */
@Data
@Schema(description = "积分统计信息")
public class SourceStatisticsVO implements Serializable {

    @Schema(description = "总积分（个）")
    private Integer totalPoints;

    @Schema(description = "累计消费积分数（个）")
    private Integer totalConsumed;

    @Schema(description = "累计奖励积分（个）")
    private Integer totalRewarded;

    @Schema(description = "累计问卷积分（个）")
    private Integer totalSurvey;

    @Schema(description = "积分使用率（%）")
    private BigDecimal usageRate;
}
