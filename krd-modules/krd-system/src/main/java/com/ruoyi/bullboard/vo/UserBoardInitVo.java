package com.ruoyi.bullboard.vo;

import lombok.Data;

import java.util.List;

/**
 * @Author: suhai
 * @Date: 2025/6/27
 * @Description: 用户数据看板初始化数据VO，包含所有看板数据
 */
@Data
public class UserBoardInitVo {

    /**
     * 用户数据看板头部信息
     */
    private UserBoardTopVo userBoardTop;

    /**
     * 用户数据折线图数据
     */
    private List<UserDataLineVo> userDataLine;

    /**
     * 用户新增统计信息
     */
    private UserAddNewInfo userAddNewInfo;

    /**
     * 用户性别占比数据（新增用户）
     */
    private UserAccountVo newUserAccount;

    /**
     * 用户性别占比数据（活跃用户）
     */
    private UserAccountVo activeUserAccount;

    /**
     * 用户性别占比数据（累计用户）
     */
    private UserAccountVo totalUserAccount;

    /**
     * 用户使用数据
     */
    private List<UserDataInfoVo> userDataInfo;

    /**
     * 新增用户年龄分布
     */
    private List<UserAgeDistributionVo> newUserAgeDistribution;

    /**
     * 活跃用户年龄分布
     */
    private List<UserAgeDistributionVo> activeUserAgeDistribution;

    /**
     * 累计用户年龄分布
     */
    private List<UserAgeDistributionVo> totalUserAgeDistribution;

    /**
     * 新增用户地域分布
     */
    private List<UserRegionDistributionVo> newUserRegionDistribution;

    /**
     * 活跃用户地域分布
     */
    private List<UserRegionDistributionVo> activeUserRegionDistribution;

    /**
     * 累计用户地域分布
     */
    private List<UserRegionDistributionVo> totalUserRegionDistribution;

    /**
     * 用户标签分布
     */
    private List<UserTagDistributionVo> userTagDistribution;
}
