package com.ruoyi.store.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName FrontGiftVo
 * @Description 后台 —— 前后端交互数据
 * <AUTHOR>
 * @Date 2025/5/20 上午10:22
 */
public interface FrontGiftVo {

    @Data
    @Schema(description = "礼品卡列表")
    class FrontGiftList {
        @Schema(name = "id")
        private Long id;
        @Schema(name = "礼品卡编号")
        private String giftNo;
        @Schema(name = "礼品卡名称")
        private String name;
        @Schema(name = "总发行量")
        private Integer total;
        @Schema(name = "面值")
        private BigDecimal balance;
        @Schema(name = "有效期限")
        private String expirationDate;
        @Schema(name = "有效时间")
        private String validTime;
        @Schema(name = "已售")
        private Long sold;
        @Schema(name = "已使用")
        private Long used;
        @Schema(name = "使用中")
        private Long using;
        @Schema(name = "礼品卡描述")
        private String remark;
        @Schema(name = "上下架 0-下架 1-上架")
        private Integer isShelf;
    }

    @Data
    @Schema(description = "礼品卡搜索参数")
    class FrontGiftSearch {
        @Schema(name = "礼品卡名称 || 礼品卡编号")
        private String keyword;
        @Schema(name = "礼品卡状态 0-待使用 1-已使用(包含使用中) 2-已过期")
        private String status;
    }
}
