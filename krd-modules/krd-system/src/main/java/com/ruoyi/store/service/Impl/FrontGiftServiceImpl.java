package com.ruoyi.store.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.FrontGift;
import com.ruoyi.system.api.domain.FrontGiftInfo;
import com.ruoyi.store.domain.vo.FrontGiftVo;
import com.ruoyi.system.api.domain.StoreFrontCoupon;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.system.api.mapper.FrontGiftMapper;
import com.ruoyi.store.service.IFrontGiftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName FrontGiftServiceImpl
 * @Description 后台商城管理 —— 礼品卡业务实现类
 * <AUTHOR>
 * @Date 2025/5/19 下午5:44
 */
@Slf4j
@Service
public class FrontGiftServiceImpl extends ServiceImpl<FrontGiftMapper, FrontGift> implements IFrontGiftService{

    @Autowired
    private FrontGiftInfoMapper frontGiftInfoMapper;

    @Override
    public List<FrontGiftVo.FrontGiftList> getGiftList(FrontGiftVo.FrontGiftSearch vo) {
        List<FrontGiftVo.FrontGiftList> frontGiftList = new ArrayList<>();
        // 查询未删除的礼品卡
        LambdaQueryWrapper<FrontGift> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FrontGift::getIsDel, 0);

        // 判断搜索参数不为null
        if (vo.getKeyword() != null){
             // 查询 id或者name
            queryWrapper.like(FrontGift::getId, vo.getKeyword())
                    .or()
                    .like(FrontGift::getName, vo.getKeyword());
        }
        List<FrontGift> frontGifts = this.list(queryWrapper);
        if (!frontGifts.isEmpty()){
            // 遍历frontGifts
            frontGifts.forEach(frontGift -> {
                // 获取礼品卡id 查询礼品卡详情
                LambdaQueryWrapper<FrontGiftInfo> lqw = new LambdaQueryWrapper<>();
                lqw.eq(FrontGiftInfo::getGiftId, frontGift.getId());

                // 判断搜索参数不为null
                if (vo.getStatus() != null){
                    if ("1".equals(vo.getStatus())){
                        lqw.in(FrontGiftInfo::getStatus, "1","3");
                    }else {
                        lqw.eq(FrontGiftInfo::getStatus, vo.getStatus());
                    }
                }

                FrontGiftVo.FrontGiftList giftList = new FrontGiftVo.FrontGiftList();

                // 组装对象
                BeanUtils.copyProperties(frontGift, giftList);
                giftList.setSold(frontGiftInfoMapper.selectCount(lqw));
                giftList.setUsed(frontGiftInfoMapper.selectCount(lqw.eq(FrontGiftInfo::getStatus, "1")));
                giftList.setUsing(frontGiftInfoMapper.selectCount(lqw.eq(FrontGiftInfo::getStatus, "3")));

                // 有效期 为 创建时间 + 有效期天数
                if ("0".equals(frontGift.getExpirationDate())){
                    giftList.setValidTime("长期有效");
                }else {
                    giftList.setValidTime(frontGift.getExpirationDate() + "天");
                }

                giftList.setIsShelf(frontGift.getIsShelf());

                frontGiftList.add(giftList);
            });
        }
        return frontGiftList;
    }

    @Override
    public Boolean addGift(FrontGift frontGift) {
        if (frontGift.getValidDays() != null){
            frontGift.setExpirationDate(String.valueOf(frontGift.getValidDays()));
        }
        frontGift.setCreateTime(LocalDateTime.now());
        return this.save(frontGift);
    }

    @Override
    public Boolean updateGift(FrontGift frontGift) {
        if (frontGift.getValidDays() != null){
            frontGift.setExpirationDate(String.valueOf(frontGift.getValidDays()));
        }
        frontGift.setUpdateTime(LocalDateTime.now());
        return this.updateById(frontGift);
    }

    @Override
    @Transactional
    public void deleteGiftByIds(Long[] ids) {
        for (Long id : ids) {
            this.removeById(id);
        }
    }

    @Override
    public Boolean updateStatus(Long id) {
        // 查询当前优惠券详情
        FrontGift frontGift = this.getById(id);
        if (frontGift != null){
            frontGift.setIsShelf(frontGift.getIsShelf() == 0 ? 1 : 0);
            this.updateById(frontGift);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
