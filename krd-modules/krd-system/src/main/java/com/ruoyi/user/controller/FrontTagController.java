package com.ruoyi.user.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.user.FrontTag;
import com.ruoyi.user.service.IFrontTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 标签管理Controller
 *
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/frontTag")
public class FrontTagController extends BaseController
{
    @Autowired
    private IFrontTagService frontTagService;

    /**
     * 查询标签管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(FrontTag frontTag)
    {
        startPage();
        List<FrontTag> list = frontTagService.selectFrontTagList(frontTag);
        return getDataTable(list);
    }

    /**
     * 导出标签管理列表
     */
    @RequiresPermissions("system:tag:export")
    @Log(title = "标签管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontTag frontTag)
    {
        List<FrontTag> list = frontTagService.selectFrontTagList(frontTag);
        ExcelUtil<FrontTag> util = new ExcelUtil<FrontTag>(FrontTag.class);
        util.exportExcel(response, list, "标签管理数据");
    }

    /**
     * 获取标签管理详细信息
     */
    @RequiresPermissions("system:tag:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(frontTagService.selectFrontTagById(id));
    }

    /**
     * 新增标签管理
     */
    @RequiresPermissions("system:tag:add")
    @Log(title = "标签管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FrontTag frontTag)
    {
        return toAjax(frontTagService.insertFrontTag(frontTag));
    }

    /**
     * 修改标签管理
     */
    @RequiresPermissions("system:tag:edit")
    @Log(title = "标签管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FrontTag frontTag)
    {
        return toAjax(frontTagService.updateFrontTag(frontTag));
    }

    /**
     * 删除标签管理
     */
    @RequiresPermissions("system:tag:remove")
    @Log(title = "标签管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontTagService.deleteFrontTagByIds(ids));
    }
}
