<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bullboard.mapper.GiftMapper">

    <select id="getGiftStatistics" resultType="com.ruoyi.bullboard.vo.GiftStatisticsVO">
        SELECT
            COALESCE(SUM(fgi.balance), 0) AS totalSales,
            COALESCE(SUM(CASE WHEN fgi.status = '0' THEN fgi.balance ELSE 0 END), 0) AS pendingUse,
            COALESCE(SUM(CASE WHEN fgi.status = '1' THEN fgi.balance ELSE 0 END), 0) AS totalUsed,
            CASE
                WHEN SUM(fg.balance * fg.total) > 0 THEN
                    ROUND(SUM(fgi.balance) * 100.0 / SUM(fg.balance * fg.total), 2)
                ELSE 0.00
            END AS purchaseRate,
            CASE
                WHEN SUM(fgi.balance) > 0 THEN
                    ROUND(SUM(CASE WHEN fgi.status = '1' THEN fgi.balance ELSE 0 END) * 100.0 / SUM(fgi.balance), 2)
                ELSE 0.00
            END AS usageRate
        FROM front_gift fg
        LEFT JOIN front_gift_info fgi ON fg.id = fgi.gift_id
        WHERE fg.is_del = 0
    </select>

    <select id="getGiftLine" resultType="com.ruoyi.bullboard.vo.GiftLineVo">
        WITH RECURSIVE date_series AS (
            SELECT #{startDate} AS date
            UNION ALL
            SELECT DATE_ADD(date, INTERVAL 1 DAY)
            FROM date_series
            WHERE date &lt; #{endDate}
        )
        SELECT
            ds.date,
            COALESCE(
                (SELECT SUM(fgi.balance)
                 FROM front_gift_info fgi
                 INNER JOIN front_gift fg ON fgi.gift_id = fg.id
                 WHERE fg.is_del = 0
                   AND DATE(fgi.create_time) &lt;= ds.date), 0
            ) AS totalSales,
            COALESCE(
                (SELECT SUM(fgi.balance)
                 FROM front_gift_info fgi
                 INNER JOIN front_gift fg ON fgi.gift_id = fg.id
                 WHERE fg.is_del = 0
                   AND fgi.status = '0'
                   AND DATE(fgi.create_time) &lt;= ds.date
                   AND (fgi.use_time IS NULL OR DATE(fgi.use_time) > ds.date)), 0
            ) AS pendingUse,
            COALESCE(
                (SELECT SUM(fgi.balance)
                 FROM front_gift_info fgi
                 INNER JOIN front_gift fg ON fgi.gift_id = fg.id
                 WHERE fg.is_del = 0
                   AND fgi.status = '1'
                   AND DATE(fgi.use_time) = ds.date), 0
            ) AS totalUsed
        FROM date_series ds
        ORDER BY ds.date
    </select>
</mapper>
