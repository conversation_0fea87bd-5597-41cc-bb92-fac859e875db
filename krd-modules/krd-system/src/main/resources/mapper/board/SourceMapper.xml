<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bullboard.mapper.SourceMapper">

    <select id="getSourceStatistics" resultType="com.ruoyi.bullboard.vo.SourceStatisticsVO">
        SELECT
            COALESCE(SUM(CASE WHEN type = 0 THEN point ELSE 0 END), 0) AS totalPoints,
            COALESCE(SUM(CASE WHEN type = 1 THEN point ELSE 0 END), 0) AS totalConsumed,
            COALESCE(SUM(CASE WHEN type = 0 THEN point ELSE 0 END), 0) AS totalRewarded,
            COALESCE(SUM(CASE WHEN type = 0 AND source = 1 THEN point ELSE 0 END), 0) AS totalSurvey,
            CASE
                WHEN SUM(CASE WHEN type = 0 THEN point ELSE 0 END) > 0 THEN
                    ROUND(SUM(CASE WHEN type = 1 THEN point ELSE 0 END) * 100.0 / SUM(CASE WHEN type = 0 THEN point ELSE 0 END), 2)
                ELSE 0.00
            END AS usageRate
        FROM front_source
    </select>

    <select id="getSourceLine" resultType="com.ruoyi.bullboard.vo.SourceLineVo">
        WITH RECURSIVE date_series AS (
            SELECT #{startDate} AS date
            UNION ALL
            SELECT DATE_ADD(date, INTERVAL 1 DAY)
            FROM date_series
            WHERE date &lt; #{endDate}
        )
        SELECT
            ds.date,
            COALESCE(
                (SELECT SUM(point)
                 FROM front_source fs
                 WHERE fs.type = 0
                   AND fs.source IN (2, 3, 4, 6)
                   AND DATE(fs.create_time) &lt;= ds.date), 0
            ) AS totalActivityPoints,
            COALESCE(
                (SELECT SUM(point)
                 FROM front_source fs
                 WHERE fs.type = 0
                   AND fs.source = 0
                   AND DATE(fs.create_time) &lt;= ds.date), 0
            ) AS totalSignPoints,
            COALESCE(
                (SELECT SUM(point)
                 FROM front_source fs
                 WHERE fs.type = 0
                   AND fs.source = 1
                   AND DATE(fs.create_time) &lt;= ds.date), 0
            ) AS totalSurveyPoints
        FROM date_series ds
        ORDER BY ds.date
    </select>
</mapper>
