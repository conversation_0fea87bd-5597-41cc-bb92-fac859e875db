<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.api.mapper.FrontEvaluateMapper">

    <select id="selectEvaluateList" resultType="com.ruoyi.system.api.domain.vo.FrontEvaluateVo$FrontEvaluateList">
        SELECT
            g.id,
            g.first_pic,
            g.goods_type AS type,
            case when g.gategory_id is null then g.package_id else g.gategory_id end AS categoryName,
            g.`name`,
            g.price,
            og.price as vipPrice,
            g.is_status,
            g.amount,
            COALESCE(og.order_count, 0) AS orderCount, -- 订单商品记录数
            COALESCE(e.eval_type0, 0) AS replyGood, -- 类型好评评价数
            COALESCE(e.eval_type1, 0) AS replyMiddle, -- 类型中评评价数
            COALESCE(e.eval_type2, 0) AS replyBad -- 类型差评评价数
        FROM
            front_goods g
        LEFT JOIN (
            SELECT
            goods_id,
            price,
            COUNT(*) AS order_count -- 订单商品记录数
            FROM
            front_orders_goods
            GROUP BY
            goods_id
        ) og ON g.id = og.goods_id
        LEFT JOIN (
            SELECT
            goods_id,
            SUM(type = 0) AS eval_type0, -- 统计type=0
            SUM(type = 1) AS eval_type1, -- 统计type=1
            SUM(type = 2) AS eval_type2 -- 统计type=2
            FROM
            front_evaluate
            GROUP BY
            goods_id
        ) e ON g.id = e.goods_id
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                AND g.name LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.type != null">
                AND g.goods_type = #{query.type}
            </if>
            <if test="query.categoryId != null and query.categoryId != ''">
                AND g.gategory_id = #{query.categoryId} or g.package_id = #{query.categoryId}
            </if>
        </where>
    </select>

    <select id="selectGoodsEvaluateDetail" resultType="com.ruoyi.system.api.domain.vo.FrontEvaluateVo$FrontEvaluateDetailInfo">
        SELECT
            g.id AS goodsId,
            g.first_pic,
            g.`name`,
            e.star_rating AS starRating,
            COALESCE(og.order_count, 0) AS totalCount,
            COALESCE(og.monthly_sales, 0) AS orderCount,  -- 新增月销量
            COALESCE(e.eval_type0, 0) AS totalReplyGood,
            COALESCE(e.eval_type1, 0) AS totalReplyMiddle,
            COALESCE(e.eval_type2, 0) AS totalReplyBad
        FROM
            front_goods g
                LEFT JOIN (
                SELECT
                    goods_id,
                    COUNT(*) AS order_count,  -- 总销量
                    COUNT(CASE WHEN create_time >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) THEN 1 END) AS monthly_sales  -- 月销量
                FROM
                    front_orders_goods
                GROUP BY
                    goods_id
            ) og ON g.id = og.goods_id
                LEFT JOIN (
                SELECT
                    goods_id,
                    SUM(type = 0) AS eval_type0,
                    SUM(type = 1) AS eval_type1,
                    SUM(type = 2) AS eval_type2,
                    (SUM(star_rating) / COUNT(*)) AS star_rating
                FROM
                    front_evaluate
                GROUP BY
                    goods_id
        ) e ON g.id = e.goods_id
        <where>
            <if test="query.goodsId != null and query.goodsId != ''">
                AND e.goods_id = #{query.goodsId}
            </if>
        </where>
    </select>
</mapper>