package com.ruoyi.uni.uni.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.resp.FrontUserMoneyResp;
import com.ruoyi.system.api.domain.vo.FrontEntity;
import com.ruoyi.uni.wxpay.service.IFrontUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户Controller
 *
 * @date 2025-05-09
 */
@RestController
@Tag(name = "用户管理", description = "用户列表")
@RequestMapping("/frontUser")
public class FrontUserController extends BaseController
{
    @Autowired
    private IFrontUserService frontUserService;

    /**
     * 查询用户列表
     */
    @Operation(summary = "查询用户列表", description = "查询用户列表")
    @GetMapping("/list")
    public TableDataInfo list(FrontEntity frontUser)
    {
        startPage();
        List<FrontEntity> list = frontUserService.selectFrontUserList(frontUser);
        return getDataTable(list);
    }
    @Operation(summary = "钱包明细", description = "钱包明细 0-余额 1-礼品卡 2-优惠券 3-积分")
    @GetMapping("/moneyPackage/listInfo/{type}/{userId}")
    public TableDataInfo listMoneyPackageInfo(@PathVariable("type") String type,@PathVariable("userId") Long userId)
    {
        startPage();
        List<FrontUserMoneyResp> list = frontUserService.listMoneyPackageInfo(type,userId);
        return getDataTable(list);
    }


    /**
     * 导出用户列表
     */
    @Operation(summary = "导出用户列表", description = "导出用户列表")
    @RequiresPermissions("system:user:export")
    @Log(title = "用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FrontEntity frontUser)
    {
        List<FrontEntity> list = frontUserService.selectFrontUserList(frontUser);
        ExcelUtil<FrontEntity> util = new ExcelUtil<FrontEntity>(FrontEntity.class);
        util.exportExcel(response, list, "用户数据");
    }

    /**
     * 获取用户详细信息
     */
    @Operation(summary = "获取用户详细信息", description = "获取用户详细信息")
    @RequiresPermissions("system:user:query")
//    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {

        FrontUser frontUser = frontUserService.selectFrontUserById(id);
        return success(frontUser);
    }


    /**
     * 冻结用户
     */
    @Operation(summary = "冻结用户", description = "冻结用户")
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(frontUserService.deleteFrontUserByIds(ids));
    }

}
