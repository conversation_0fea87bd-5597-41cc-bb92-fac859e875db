package com.ruoyi.uni.uni.service.Impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.system.api.mapper.FrontGiftMapper;
import com.ruoyi.system.api.utils.CommonUtil;
import com.ruoyi.system.api.utils.WxPayUtil;
import com.ruoyi.system.api.vo.AttachVo;
import com.ruoyi.system.api.vo.CreateOrderRequestVo;
import com.ruoyi.system.api.vo.CreateOrderResponseVo;
import com.ruoyi.uni.uni.domain.user.FrontUserToken;
import com.ruoyi.uni.uni.mapper.FrontUserTokenMapper;
import com.ruoyi.uni.uni.service.IFrontOrdersService;
import com.ruoyi.uni.uni.service.UserRechargeService;
import com.ruoyi.uni.wechat.service.WechatNewService;
import com.ruoyi.uni.wxpay.factory.PaymentStrategyFactory;
import com.ruoyi.uni.wxpay.request.OrderPayRequest;
import com.ruoyi.uni.wxpay.request.UserGiftRequest;
import com.ruoyi.uni.wxpay.request.UserRechargeRequest;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.service.IFrontUserService;
import com.ruoyi.uni.wxpay.service.OrderService;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import com.ruoyi.uni.wxpay.vo.CreateOrderH5SceneInfoDetailVo;
import com.ruoyi.uni.wxpay.vo.CreateOrderH5SceneInfoVo;
import com.ruoyi.uni.wxpay.vo.WxPayJsResultVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description: 小程序订单服务实现
 */
@RequiredArgsConstructor
@Service
public class OrderServiceImpl implements OrderService {

    private final IFrontOrdersService orderService;

    private final IFrontUserService userService;

    private final FrontUserTokenMapper userTokenMapper;

    private final WechatNewService wechatNewService;

    private final UserRechargeService userRechargeService;

    private final PaymentStrategyFactory paymentStrategyFactory;

    private final FrontGiftMapper frontGiftMapper;

    private final FrontGiftInfoMapper frontGiftInfoMapper;


    /**
     * 订单支付
     *
     * @param orderPayRequest 支付参数
     * @param ip              ip
     * @return OrderPayResultResponse
     */
    @Override
    @Transactional
    public OrderPayResultResponse payment(OrderPayRequest orderPayRequest, String ip) {
        FrontOrders frontOrders = orderService.selectOrderNum(orderPayRequest.getOrderNo());
        validateOrder(frontOrders);
        frontOrders.setPayType(orderPayRequest.getPayType());
        frontOrders.setCouponInfoId(orderPayRequest.getCouponInfoId());
         // 验证请求参数
        validateRequest(orderPayRequest, frontOrders);
        // 更新订单信息
        orderService.updateById(frontOrders);
        FrontUser frontUser = userService.queryFrontUserById(frontOrders.getUserId());
        if (ObjectUtil.isNull(frontUser)) throw new GlobalException("用户不存在");
        String payTypes = frontOrders.getPayType();
        // 支持多种支付方式组合，按逗号分割
        String[] payTypeArray = payTypes.split(",");
        OrderPayResultResponse finalResponse = null;
        for (String payType : payTypeArray) {
            PaymentStrategy paymentStrategy = paymentStrategyFactory.getStrategy(payType);
            if (paymentStrategy == null) {
                throw new GlobalException("不支持的支付方式: " + payType);
            }
            finalResponse = paymentStrategy.pay(frontOrders, ip);
        }
//        orderService.updateById(frontOrders);
        // 处理订单金额 订单对应商品金额
        System.out.println("对象标签"+frontOrders);
        return finalResponse;
    }

    public void validateRequest(OrderPayRequest orderPayRequest, FrontOrders frontOrders) {
        Optional.ofNullable(orderPayRequest.getPoints()).ifPresent(frontOrders::setPointsDeduction);
        Optional.ofNullable(orderPayRequest.getBalance()).ifPresent(frontOrders::setUseBalance);
        Optional.ofNullable(orderPayRequest.getGiftDeduction()).ifPresent(frontOrders::setGiftDeduction);
        Optional.ofNullable(orderPayRequest.getRemark()).ifPresent(frontOrders::setUserRemark);
        Optional.ofNullable(orderPayRequest.getReceiveName()).ifPresent(frontOrders::setReceiveName);
        Optional.ofNullable(orderPayRequest.getReceivePhone()).ifPresent(frontOrders::setReceivePhone);
        Optional.ofNullable(orderPayRequest.getReceiveAddress()).ifPresent(frontOrders::setReceiveAddress);
        Optional.ofNullable(orderPayRequest.getReceiveAddressDetail()).ifPresent(frontOrders::setReceiveAddressDetail);
    }
    public void validateOrder(FrontOrders frontOrders) {
        if (ObjectUtil.isNull(frontOrders)) {
            throw new GlobalException("订单不存在");
        }
        if (frontOrders.getIsDel()) {
            throw new GlobalException("订单已被删除");
        }
        if (frontOrders.getPaid()) {
            throw new GlobalException("订单已支付");
        }
    }

    // 充值
    @Override
    public OrderPayResultResponse recharge(UserRechargeRequest request) {
        request.setPayType(PayConstants.PAY_TYPE_WE_CHAT);

        //验证金额是否为最低金额
        String rechargeMinAmountStr = "0.01";
        BigDecimal rechargeMinAmount = new BigDecimal(rechargeMinAmountStr);
        int compareResult = rechargeMinAmount.compareTo(request.getPrice());
        if (compareResult > 0) {
            throw new GlobalException("充值金额不能低于" + rechargeMinAmountStr);
        }
        FrontLoginUser currentUser = SecurityUtils.getLoginUser().getFrontUser();
        //生成系统订单
        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setUid(Math.toIntExact(currentUser.getId()));
        userRecharge.setOrderId(CommonUtil.getOrderNo("recharge"));
        userRecharge.setPrice(request.getPrice());
        userRecharge.setRechargeType(request.getFromType());
        boolean save = userRechargeService.save(userRecharge);
        if (!save) {
            throw new GlobalException("生成充值订单失败!");
        }
        OrderPayResultResponse response = new OrderPayResultResponse();
        MyRecord record = new MyRecord();
        Map<String, String> unifiedorder = unifiedRecharge(userRecharge, request.getClientIp(), "recharge");
        record.set("status", true);
        response.setStatus(true);
        WxPayJsResultVo vo = new WxPayJsResultVo();
        vo.setAppId(unifiedorder.get("appId"));
        vo.setNonceStr(unifiedorder.get("nonceStr"));
        vo.setPackages(unifiedorder.get("package"));
        vo.setSignType(unifiedorder.get("signType"));
        vo.setTimeStamp(unifiedorder.get("timeStamp"));
        vo.setPaySign(unifiedorder.get("paySign"));
        if (userRecharge.getRechargeType().equals(PayConstants.PAY_CHANNEL_WE_CHAT_H5)) {
            vo.setMwebUrl(unifiedorder.get("mweb_url"));
            response.setPayType(PayConstants.PAY_CHANNEL_WE_CHAT_H5);
        }
        response.setJsConfig(vo);
        response.setOrderNo(userRecharge.getOrderId());
        return response;
    }

    @Override
    public OrderPayResultResponse gift(UserGiftRequest request) {
        request.setPayType(PayConstants.PAY_TYPE_WE_CHAT);

        // 查询对应礼品卡
        FrontGift frontGift = frontGiftMapper.selectById(request.getId());
        if (ObjectUtil.isNull(frontGift)) {
            throw new GlobalException("礼品卡不存在");
        }

        if (frontGift.getTotal() > 0) {
            // 查询已购买数量
            Long count = frontGiftInfoMapper.selectCount(new LambdaQueryWrapper<FrontGiftInfo>().eq(FrontGiftInfo::getGiftId, request.getId()));
            if (count >= frontGift.getTotal()) {
                throw new GlobalException("礼品卡暂无库存");
            }
        }

        FrontLoginUser currentUser = SecurityUtils.getLoginUser().getFrontUser();

        //生成系统订单
        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setUid(Math.toIntExact(currentUser.getId()));
        userRecharge.setOrderId(CommonUtil.getOrderNo("gift"));
        userRecharge.setPrice(frontGift.getBalance());
        userRecharge.setRechargeType(request.getFromType());
        userRecharge.setGiftId(request.getId());
        boolean save = userRechargeService.save(userRecharge);
        if (!save) {
            throw new GlobalException("生成礼品卡订单失败!");
        }
        OrderPayResultResponse response = new OrderPayResultResponse();
        MyRecord record = new MyRecord();
        Map<String, String> unifiedorder = unifiedRecharge(userRecharge, request.getClientIp(), "gift");
        record.set("status", true);
        response.setStatus(true);
        WxPayJsResultVo vo = new WxPayJsResultVo();
        vo.setAppId(unifiedorder.get("appId"));
        vo.setNonceStr(unifiedorder.get("nonceStr"));
        vo.setPackages(unifiedorder.get("package"));
        vo.setSignType(unifiedorder.get("signType"));
        vo.setTimeStamp(unifiedorder.get("timeStamp"));
        vo.setPaySign(unifiedorder.get("paySign"));
        if (userRecharge.getRechargeType().equals(PayConstants.PAY_CHANNEL_WE_CHAT_H5)) {
            vo.setMwebUrl(unifiedorder.get("mweb_url"));
            response.setPayType(PayConstants.PAY_CHANNEL_WE_CHAT_H5);
        }
        response.setJsConfig(vo);
        response.setOrderNo(userRecharge.getOrderId());
        return response;
    }


    public Map<String, String> unifiedRecharge(UserRecharge userRecharge, String clientIp, String type) {
        if (ObjectUtil.isNull(userRecharge)) {
            throw new GlobalException("订单不存在");
        }
        FrontUserToken userToken = userTokenMapper.selectByUserId(Long.valueOf(userRecharge.getUid()));
        if (ObjectUtil.isNull(userToken)) {
            throw new GlobalException("该用户没有openId");
        }
        // 获取appid、mch_id
        // 微信签名key
        String appId = "wxbbfc64e72fa3980f";
        String mchId = "1720010260";
        String signKey = "frB6YEFq2FxlJrJJ6K0hATIiSawPexfg";
        // 获取微信预下单对象
        CreateOrderRequestVo unifiedorderVo = getRechargeUnifiedorderVo(userRecharge, userToken.getOpenId(), clientIp, appId, mchId, signKey, type);
        // 预下单
        CreateOrderResponseVo responseVo = wechatNewService.unifiedOrder(unifiedorderVo);
        // 组装前端预下单参数
        Map<String, String> map = new HashMap<>();
        map.put("appId", unifiedorderVo.getAppid());
        map.put("nonceStr", unifiedorderVo.getNonce_str());
        map.put("package", "prepay_id=".concat(responseVo.getPrepayId()));
        map.put("signType", unifiedorderVo.getSign_type());
        Long currentTimestamp = WxPayUtil.getCurrentTimestamp();
        map.put("timeStamp", Long.toString(currentTimestamp));
        String paySign = WxPayUtil.getSign(map, signKey);
        map.put("paySign", paySign);
        if (userRecharge.getRechargeType().equals(PayConstants.PAY_CHANNEL_WE_CHAT_H5)) {
            map.put("mweb_url", responseVo.getMWebUrl());
        }
        return map;
    }

    /**
     * 获取微信预下单对象 充值
     *
     * @return
     */
    private CreateOrderRequestVo getRechargeUnifiedorderVo(UserRecharge userRecharge, String openid, String ip, String appId, String mchId, String signKey, String type) {

        // 获取域名
        String domain = "yjt.beten.cn";
        String apiDomain = "https://yjt.beten.cn";
        String siteName = "jx";
        AttachVo attachVo = new AttachVo(type, userRecharge.getUid());
        CreateOrderRequestVo vo = new CreateOrderRequestVo();
        vo.setAppid(appId);
        vo.setMch_id(mchId);
        vo.setNonce_str(WxPayUtil.getNonceStr());
        vo.setSign_type(PayConstants.WX_PAY_SIGN_TYPE_MD5);
        vo.setBody(PayConstants.PAY_BODY);
        vo.setAttach(JSONObject.toJSONString(attachVo));
        vo.setOut_trade_no(userRecharge.getOrderId());
        // 订单中使用的是BigDecimal,这里要转为Integer类型
        vo.setTotal_fee(userRecharge.getPrice().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).intValue());
        vo.setSpbill_create_ip(ip);
        vo.setNotify_url(apiDomain + PayConstants.WX_PAY_NOTIFY_API_URI);
        vo.setTrade_type(PayConstants.WX_PAY_TRADE_TYPE_JS);
        vo.setOpenid(openid);
        CreateOrderH5SceneInfoVo createOrderH5SceneInfoVo = new CreateOrderH5SceneInfoVo(
                new CreateOrderH5SceneInfoDetailVo(
                        domain,
                        siteName
                )
        );
        vo.setScene_info(JSONObject.toJSONString(createOrderH5SceneInfoVo));
        String sign = WxPayUtil.getSign(vo, signKey);
        vo.setSign(sign);
        return vo;
    }

}
