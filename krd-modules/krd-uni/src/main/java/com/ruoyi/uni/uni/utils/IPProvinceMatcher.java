package com.ruoyi.uni.uni.utils;

import java.io.*;
import java.nio.file.*;
import java.util.*;

public class IPProvinceMatcher {
    // IP段数据类
    static class IpRange {
        long startIp;    // 起始IP（长整型）
        long endIp;      // 结束IP（长整型）
        String province; // 省份名称

        public IpRange(long startIp, long endIp, String province) {
            this.startIp = startIp;
            this.endIp = endIp;
            this.province = province;
        }
    }

    private final List<IpRange> ipRanges = new ArrayList<>();

    // 加载IP库文件
    public void loadIpLibrary(String filePath) throws IOException {
        List<String> lines = Files.readAllLines(Paths.get(filePath));
        for (String line : lines) {
            if (line.trim().isEmpty() || line.startsWith("#")) continue;

            String[] parts = line.split(",");
            if (parts.length != 3) continue;

            long startIp = ipToLong(parts[0].trim());
            long endIp = ipToLong(parts[1].trim());
            String province = parts[2].trim();

            ipRanges.add(new IpRange(startIp, endIp, province));
        }
        Collections.sort(ipRanges, Comparator.comparingLong(r -> r.startIp));
    }

    // IP地址转长整型
    private long ipToLong(String ipAddress) {
        String[] octets = ipAddress.split("\\.");
        return (Long.parseLong(octets[0]) << 24) |
                (Long.parseLong(octets[1]) << 16) |
                (Long.parseLong(octets[2]) << 8)  |
                Long.parseLong(octets[3]);
    }

    // 查找IP对应的省份
    public String findProvince(String ipAddress) {
        long ip = ipToLong(ipAddress);

        // 二分查找
        int low = 0;
        int high = ipRanges.size() - 1;

        while (low <= high) {
            int mid = (low + high) >>> 1;
            IpRange range = ipRanges.get(mid);

            if (ip < range.startIp) {
                high = mid - 1;
            } else if (ip > range.endIp) {
                low = mid + 1;
            } else {
                return range.province; // 找到匹配区间
            }
        }

        return "未知"; // 未找到匹配项
    }

    public static void main(String[] args) {
        IPProvinceMatcher matcher = new IPProvinceMatcher();
        try {
            // 加载IP库（示例文件路径）
            matcher.loadIpLibrary("ip_province.csv");

            // 测试IP查询
            System.out.println("************ -> " + matcher.findProvince("************")); // 北京市
            System.out.println("*************** -> " + matcher.findProvince("***************")); // 广东省
            System.out.println("127.0.0.1 -> " + matcher.findProvince("127.0.0.1")); // 未知

        } catch (IOException e) {
            System.err.println("加载IP库失败: " + e.getMessage());
        }
    }
}