package com.ruoyi.uni.wxpay.controller;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.uni.wxpay.request.OrderPayRequest;
import com.ruoyi.uni.wxpay.request.UserGiftRequest;
import com.ruoyi.uni.wxpay.request.UserRechargeRequest;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.service.OrderService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: suhai
 * @Date: 2025/5/23
 * @Description:
 */
@RestController
@RequestMapping("/pay")
@RequiredArgsConstructor
@Tag(name = "微信订单管理", description = "微信订单管理")
public class StoreFrontOrderController extends BaseController {

    private final OrderService orderService;

    /**
     * 订单支付
     */
    @RequestMapping(value = "/payment", method = RequestMethod.POST)
    public AjaxResult request(@RequestBody @Validated OrderPayRequest orderPayRequest) {
        String ipAddr = IpUtils.getIpAddr();
        return success(orderService.payment(orderPayRequest,ipAddr));
    }

    /**
     * 小程序充值
     */
    @RequestMapping(value = "/recharge", method = RequestMethod.POST)
    public AjaxResult routineRecharge(@RequestBody @Validated UserRechargeRequest request) {
        request.setFromType(PayConstants.PAY_TYPE_WE_CHAT_FROM_PROGRAM);
        request.setClientIp(IpUtils.getIpAddr());
        OrderPayResultResponse recharge = orderService.recharge(request);
        Map<String, Object> map = new HashMap<>();
        map.put("data", recharge);
        map.put("type", request.getFromType());
        return success(map);
    }

    /**
     * 小程序礼品卡充值
     */
    @RequestMapping(value = "/gift", method = RequestMethod.POST)
    public AjaxResult routineGift(@RequestBody @Validated UserGiftRequest request) {
        request.setFromType(PayConstants.PAY_TYPE_WE_CHAT_FROM_PROGRAM);
        request.setClientIp(IpUtils.getIpAddr());
        OrderPayResultResponse gift = orderService.gift(request);
        Map<String, Object> map = new HashMap<>();
        map.put("data", gift);
        map.put("type", request.getFromType());
        return success(map);
    }

}
