package com.ruoyi.uni.wxpay.strategy;

import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.uni.wxpay.model.PaymentRecord;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:支付策略接口
 */
public interface PaymentStrategy {
    /**
     * 执行支付
     * @param order 订单
     * @param ip 客户端IP
     * @return 支付结果
     */
    OrderPayResultResponse pay(FrontOrders order, String ip);

    /**
     * 预扣除资金（用于组合支付的事务控制）
     * @param order 订单
     * @return 支付记录，包含回滚和确认操作
     */
    default PaymentRecord preDeduct(FrontOrders order) {
        // 默认实现：对于第三方支付，不需要预扣除
        return null;
    }

    /**
     * 获取支付类型标识
     * @return 支付类型
     */
    String getPayType();

    /**
     * 判断是否为第三方支付
     * @return true-第三方支付，false-内部支付
     */
    default boolean isThirdPartyPayment() {
        return false;
    }
}
