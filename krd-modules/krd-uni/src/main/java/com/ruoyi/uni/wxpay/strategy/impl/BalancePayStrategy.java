package com.ruoyi.uni.wxpay.strategy.impl;

import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.common.redis.util.RedisUtil;
import com.ruoyi.system.api.constants.TaskConstants;
import com.ruoyi.system.api.domain.FrontBalanceInfo;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.mapper.FrontBalanceInfoMapper;
import com.ruoyi.uni.wxpay.model.PaymentRecord;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.service.IFrontUserService;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 余额支付策略实现
 */
@Service
@RequiredArgsConstructor
public class BalancePayStrategy implements PaymentStrategy {

    private final IFrontUserService userService;
    private final TransactionTemplate transactionTemplate;
    private final RedisUtil redisUtil;
    private final FrontBalanceInfoMapper frontBalanceInfoMapper;

    @Override
    public OrderPayResultResponse pay(FrontOrders order, String ip) {
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(order.getOrderNumber());
        response.setPayType(getPayType());

        BigDecimal useBalance = order.getUseBalance();
        Long userId = order.getUserId();
        FrontUser user = userService.selectFrontUserById(userId);
        if (useBalance.compareTo(BigDecimal.ZERO) > 0 && user.getNowMoney().compareTo(useBalance) < 0) {
            throw new GlobalException("用户余额不足");
        }

        order.setPaid(true);
        order.setPayTime(LocalDateTime.now());
        order.setUseBalance(useBalance);

        Boolean execute = transactionTemplate.execute(e -> {
            // 修改订单状态
            order.setStatus(1);
            // 修改用户余额
            user.setNowMoney(user.getNowMoney().subtract(useBalance));
            userService.updateFrontUser(user);
            FrontBalanceInfo frontBalanceInfo = new FrontBalanceInfo();
            frontBalanceInfo.setUserId(order.getUserId());
            frontBalanceInfo.setType(0L);
            frontBalanceInfo.setMoveaccount(String.valueOf(order.getUseBalance()));
            frontBalanceInfo.setBalance(user.getNowMoney());
            frontBalanceInfo.setOrderNumber(order.getOrderNumber());
            frontBalanceInfo.setTitle("用户购买商品");
            frontBalanceInfo.setCreateTime(LocalDateTime.now());
            frontBalanceInfoMapper.insert(frontBalanceInfo);
            // 添加支付成功redis队列
            redisUtil.lPush(TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER, order);
            return Boolean.TRUE;
        });

        if (Boolean.FALSE.equals(execute)) {
            throw new GlobalException("余额支付订单失败");
        }

        response.setStatus(true);
        return response;
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_POINTS;
    }
}
