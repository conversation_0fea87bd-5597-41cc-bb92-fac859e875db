package com.ruoyi.uni.wxpay.strategy.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.system.api.domain.FrontGiftInfo;
import com.ruoyi.system.api.domain.FrontGiftUserInfo;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.vo.GiftDeduVo;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.system.api.mapper.FrontGiftUserInfoMapper;
import com.ruoyi.system.api.mapper.FrontOrdersMapper;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 礼品卡支付策略实现
 */
@Service
@RequiredArgsConstructor
public class GiftPayStrategy implements PaymentStrategy {

    private final FrontGiftInfoMapper frontGiftInfoMapper;

    private final FrontGiftUserInfoMapper frontGiftUserInfoMapper;

    private final FrontOrdersMapper frontOrdersMapper;

    @Override
    @Transactional
    public OrderPayResultResponse pay(FrontOrders order, String ip) {
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(order.getOrderNumber());
        response.setPayType(getPayType());

        BigDecimal totalGiftUsedAmount = BigDecimal.ZERO; // 初始化礼品卡总使用金额
        List<GiftDeduVo> giftDeduList = new ArrayList<>(); // 记录使用的礼品卡详情
        BigDecimal giftDeduction = order.getGiftDeduction();

        // 当礼品卡抵扣金额大于0时，进行礼品卡扣减操作
        if (giftDeduction.compareTo(BigDecimal.ZERO) > 0) {
            // 查询用户拥有的可用礼品卡列表，按使用时间升序排列（优先使用较早的礼品卡）
            List<FrontGiftInfo> giftInfoList = frontGiftInfoMapper.selectList(
                    new LambdaQueryWrapper<FrontGiftInfo>()
                            .eq(FrontGiftInfo::getUserId, order.getUserId())
                            .in(FrontGiftInfo::getStatus, "0", "3") // 0-待使用, 3-使用中
                            .orderByAsc(FrontGiftInfo::getUseTime)
                            .orderByAsc(FrontGiftInfo::getId)); // 相同使用时间按ID排序保证顺序一致

            if (giftInfoList == null || giftInfoList.isEmpty()) {
                throw new GlobalException("用户没有可用的礼品卡");
            }

            // 计算用户可用礼品卡总余额
            BigDecimal totalAvailableBalance = giftInfoList.stream()
                    .map(FrontGiftInfo::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalAvailableBalance.compareTo(giftDeduction) < 0) {
                throw new GlobalException("礼品卡余额不足，可用余额：" + totalAvailableBalance + "，需要扣减：" + giftDeduction);
            }

            // 执行礼品卡扣减逻辑，并获取使用详情
            totalGiftUsedAmount = processGiftCardDeduction(giftInfoList, giftDeduction, order.getOrderNumber(), giftDeduList);
        }

        // 将giftDeduList转为json

        ObjectMapper mapper = new ObjectMapper();
        try {
            String giftDeduJson = mapper.writeValueAsString(giftDeduList);
            // 设置订单的礼品卡使用详情
            order.setGiftList(giftDeduJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


        // 设置订单支付信息
        order.setPaid(true);
        order.setStatus(1);
        order.setPayTime(LocalDateTime.now());
        order.setGiftDeduction(totalGiftUsedAmount); // 设置礼品卡抵扣总额
        return response;
    }

    /**
     * 处理礼品卡扣减逻辑
     * @param giftInfoList 用户可用的礼品卡列表
     * @param totalDeductionAmount 总扣减金额
     * @param orderNumber 订单号
     * @param giftDeduList 用于记录使用的礼品卡详情
     * @return 实际扣减的总金额
     */
    private BigDecimal processGiftCardDeduction(List<FrontGiftInfo> giftInfoList,
                                               BigDecimal totalDeductionAmount,
                                               String orderNumber,
                                               List<GiftDeduVo> giftDeduList) {
        BigDecimal remainingDeduction = totalDeductionAmount; // 剩余需要扣减的金额
        BigDecimal totalUsedAmount = BigDecimal.ZERO; // 实际使用的总金额

        for (FrontGiftInfo giftInfo : giftInfoList) {
            if (remainingDeduction.compareTo(BigDecimal.ZERO) <= 0) {
                break; // 已经扣减完毕
            }

            BigDecimal currentBalance = giftInfo.getBalance();
            BigDecimal usedAmount; // 当前礼品卡使用的金额

            if (remainingDeduction.compareTo(currentBalance) >= 0) {
                // 需要扣减的金额 >= 当前礼品卡余额，全部使用
                usedAmount = currentBalance;
                giftInfo.setBalance(BigDecimal.ZERO);
                giftInfo.setStatus("1"); // 设置为已使用
            } else {
                // 需要扣减的金额 < 当前礼品卡余额，部分使用
                usedAmount = remainingDeduction;
                giftInfo.setBalance(currentBalance.subtract(usedAmount));
                giftInfo.setStatus("3"); // 设置为使用中
            }

            // 更新礼品卡信息
            giftInfo.setUseTime(LocalDateTime.now());
            frontGiftInfoMapper.updateById(giftInfo);

            // 记录用户使用明细
            FrontGiftUserInfo userInfo = new FrontGiftUserInfo();
            userInfo.setGiftInfoId(giftInfo.getId());
            userInfo.setOrderNo(orderNumber);
            userInfo.setType(0); // 0-支出
            userInfo.setBalance(usedAmount);
            userInfo.setCreateTime(LocalDateTime.now());
            frontGiftUserInfoMapper.insert(userInfo);

            // 创建GiftDeduVo对象记录礼品卡使用详情
            GiftDeduVo giftDeduVo = new GiftDeduVo();
            giftDeduVo.setId(giftInfo.getId());
            giftDeduVo.setAmount(usedAmount);
            giftDeduList.add(giftDeduVo);

            // 更新统计信息
            totalUsedAmount = totalUsedAmount.add(usedAmount);
            remainingDeduction = remainingDeduction.subtract(usedAmount);
        }

        return totalUsedAmount;
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_GIFT;
    }
}
