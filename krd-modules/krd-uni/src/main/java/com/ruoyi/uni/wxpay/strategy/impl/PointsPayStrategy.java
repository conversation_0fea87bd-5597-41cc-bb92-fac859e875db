package com.ruoyi.uni.wxpay.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.common.redis.util.RedisUtil;
import com.ruoyi.system.api.constants.TaskConstants;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.domain.FrontUser;
import com.ruoyi.system.api.domain.SysConfig;
import com.ruoyi.system.api.domain.user.FrontSource;
import com.ruoyi.system.api.mapper.FrontOrdersMapper;
import com.ruoyi.system.api.mapper.FrontSourceMapper;
import com.ruoyi.system.api.mapper.SysConfigMapper;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wxpay.service.IFrontUserService;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 积分支付策略实现
 */
@Service
@RequiredArgsConstructor
public class PointsPayStrategy implements PaymentStrategy {

    private final IFrontUserService userService;
    private final TransactionTemplate transactionTemplate;
    private final RedisUtil redisUtil;
    private final FrontSourceMapper frontSourceMapper;
    private final SysConfigMapper sysConfigMapper;
    private final FrontOrdersMapper frontOrdersMapper;


    // 积分兑换比例：1元 = ? 积分
    private static final int POINTS_RATE = 100;

    @Override
    public OrderPayResultResponse pay(FrontOrders order  , String ip) {
        // 获取积分参数配置
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique("points_config");
        String configValue = sysConfig.getConfigValue();
        Map<String, Object> pointsConfig = JSONObject.parseObject(configValue);
        BigDecimal pointsExchange = new BigDecimal(pointsConfig.get("pointsExchange").toString());
        BigDecimal pointsExchangeAmount = new BigDecimal(pointsConfig.get("pointsExchangeAmount").toString());
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(order.getOrderNumber());
        response.setPayType(getPayType());
        BigDecimal amount = order.getPointsDeduction();
        // 计算需要的积分
        BigDecimal requiredPoints = BigDecimal.valueOf(amount.multiply(pointsExchange).divide(pointsExchangeAmount, 0, RoundingMode.HALF_UP).intValue());

        // 获取用户信息
        FrontUser user = userService.selectFrontUserById(order.getUserId());
        if (user == null) {
            throw new GlobalException("用户不存在");
        }
        // 验证用户积分是否足够
        if (user.getIntegral().compareTo(requiredPoints) < 0) {
            throw new GlobalException("用户积分不足，需要" + requiredPoints + "积分");
        }
        // 使用事务进行积分扣减和订单状态更新
        Boolean execute = transactionTemplate.execute(e -> {
            // 更新订单状态
            order.setPaid(true);
            order.setPayTime(LocalDateTime.now());
            order.setStatus(1);
            order.setUsePoint(requiredPoints);
            // 更新用户积分
            user.setIntegral(user.getIntegral().subtract(requiredPoints));
            userService.updateFrontUser(user);
            //添加到用户积分明细表里
            FrontSource source = new FrontSource();
            source.setUserId(order.getUserId());
            source.setSource(6);
            source.setType(1);
            source.setPoint(requiredPoints.intValue());
            source.setBalance(user.getIntegral());
            source.setOrderNumber(order.getOrderNumber());
            source.setCreateTime(LocalDateTime.now());
            frontSourceMapper.insert(source);
            redisUtil.lPush(TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER , order);
            return Boolean.TRUE;
        });
        if (Boolean.FALSE.equals(execute)) {
            throw new GlobalException("积分支付订单失败");
        }
        response.setStatus(true);
        return response;
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_POINTS;
    }
}
