package com.ruoyi.uni.wxpay.strategy.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.system.api.domain.FrontOrders;
import com.ruoyi.system.api.utils.CommonUtil;
import com.ruoyi.system.api.utils.WxPayUtil;
import com.ruoyi.system.api.vo.AttachVo;
import com.ruoyi.system.api.vo.CreateOrderRequestVo;
import com.ruoyi.system.api.vo.CreateOrderResponseVo;
import com.ruoyi.uni.uni.domain.user.FrontUserToken;
import com.ruoyi.uni.uni.mapper.FrontUserTokenMapper;
import com.ruoyi.uni.uni.service.IFrontOrdersService;
import com.ruoyi.uni.wxpay.response.OrderPayResultResponse;
import com.ruoyi.uni.wechat.service.WechatNewService;
import com.ruoyi.uni.wxpay.strategy.PaymentStrategy;
import com.ruoyi.uni.wxpay.utils.DateUtil;
import com.ruoyi.uni.wxpay.vo.CreateOrderH5SceneInfoDetailVo;
import com.ruoyi.uni.wxpay.vo.CreateOrderH5SceneInfoVo;
import com.ruoyi.uni.wxpay.vo.WxPayJsResultVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: suhai
 * @Date: 2025/6/19
 * @Description:微信支付策略实现
 */
@Service
@RequiredArgsConstructor
public class WechatPayStrategy implements PaymentStrategy {

    private final WechatNewService wechatNewService;
    private final FrontUserTokenMapper userTokenMapper;
    private final IFrontOrdersService orderService;

    @Override
    public OrderPayResultResponse pay(FrontOrders order, String ip) {
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(order.getOrderNumber());
        response.setPayType(getPayType());

        BigDecimal giftDeduction = order.getGiftDeduction() == null ? BigDecimal.ZERO : order.getGiftDeduction();
        BigDecimal useBalance = order.getUseBalance() == null ? BigDecimal.ZERO : order.getUseBalance();
        BigDecimal couponDeduction = order.getCouponDeduction() == null ? BigDecimal.ZERO : order.getCouponDeduction();
        BigDecimal pointsDeduction = order.getPointsDeduction() == null ? BigDecimal.ZERO : order.getPointsDeduction();

        // 微信支付金额
        BigDecimal payPrice = order.getTotalPrice().subtract(giftDeduction).subtract(useBalance).subtract(couponDeduction).subtract(pointsDeduction);
        order.setPayPrice(payPrice);

        // 预下单
        Map<String, String> unifiedorder = unifiedorder(order, ip);
        response.setStatus(true);
        WxPayJsResultVo vo = new WxPayJsResultVo();
        vo.setAppId(unifiedorder.get("appId"));
        vo.setNonceStr(unifiedorder.get("nonceStr"));
        vo.setPackages(unifiedorder.get("package"));
        vo.setSignType(unifiedorder.get("signType"));
        vo.setTimeStamp(unifiedorder.get("timeStamp"));
        vo.setPaySign(unifiedorder.get("paySign"));
        vo.setPartnerid(unifiedorder.get("partnerid"));

        // 更新商户订单号
        order.setOutTradeNo(unifiedorder.get("outTradeNo"));
        orderService.updateFrontOrdersStatus(order);
        response.setJsConfig(vo);
        return response;
    }

    @Override
    public String getPayType() {
        return PayConstants.PAY_TYPE_WE_CHAT;
    }

    /**
     * 支付预下单
     * @param order 订单
     * @param ip ip
     * @return 预下单返回对象
     */
    private Map<String, String> unifiedorder(FrontOrders order, String ip) {
        // 获取用户openId
        FrontUserToken userToken = userTokenMapper.selectByUserId(order.getUserId());
        if (ObjectUtil.isNull(userToken)) {
            throw new GlobalException("该用户没有openId");
        }

        // 微信签名key （科讯云测试用）
        String appId = "wxbbfc64e72fa3980f";
        String mchId = "1720010260";
        String signKey = "frB6YEFq2FxlJrJJ6K0hATIiSawPexfg";

        // 获取微信预下单对象
        CreateOrderRequestVo unifiedorderVo = getUnifiedorderVo(order, userToken.getOpenId(), ip, appId, mchId, signKey);

        // 预下单（统一下单）
        CreateOrderResponseVo responseVo = wechatNewService.unifiedOrder(unifiedorderVo);
        // 组装前端预下单参数
        Map<String, String> map = new HashMap<>();
        map.put("appId", unifiedorderVo.getAppid());
        map.put("nonceStr", unifiedorderVo.getNonce_str());
        map.put("package", "prepay_id=".concat(responseVo.getPrepayId()));
        map.put("signType", unifiedorderVo.getSign_type());
        Long currentTimestamp = WxPayUtil.getCurrentTimestamp();
        map.put("timeStamp", Long.toString(currentTimestamp));
        String paySign = WxPayUtil.getSign(map, signKey);
        map.put("paySign", paySign);
        map.put("prepayId", responseVo.getPrepayId());
        map.put("prepayTime", DateUtil.nowDateTimeStr());
        map.put("outTradeNo", unifiedorderVo.getOut_trade_no());
        return map;
    }

    /**
     * 获取微信预下单对象
     * @return 微信预下单对象
     */
    private CreateOrderRequestVo getUnifiedorderVo(FrontOrders frontOrders, String openid, String ip, String appId, String mchId, String signKey) {
        String domain = "yjt.beten.cn";
        String apiDomain = "https://yjt.beten.cn";
        String siteName = "jx";
        AttachVo attachVo = new AttachVo("order", Math.toIntExact(frontOrders.getUserId()));
        CreateOrderRequestVo vo = new CreateOrderRequestVo();
        vo.setAppid(appId);
        vo.setMch_id(mchId);
        vo.setNonce_str(WxPayUtil.getNonceStr());
        vo.setSign_type(PayConstants.WX_PAY_SIGN_TYPE_MD5);
        // 因商品名称在微信侧超长更换为网站名称
        vo.setBody(siteName);
        vo.setAttach(JSONObject.toJSONString(attachVo));
        vo.setOut_trade_no(CommonUtil.getOrderNo("order"));
        // 订单中使用的是BigDecimal,这里要转为Integer类型
        vo.setTotal_fee(frontOrders.getPayPrice().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).intValue());
        vo.setSpbill_create_ip(ip);
        //回调地址 后续跟线上配置保持一致
        vo.setNotify_url(apiDomain + PayConstants.WX_PAY_NOTIFY_API_URI);
        vo.setTrade_type(PayConstants.WX_PAY_TRADE_TYPE_JS);
        vo.setOpenid(openid);
        CreateOrderH5SceneInfoVo createOrderH5SceneInfoVo = new CreateOrderH5SceneInfoVo(
                new CreateOrderH5SceneInfoDetailVo(
                        domain,
                        siteName
                )
        );
        vo.setScene_info(JSONObject.toJSONString(createOrderH5SceneInfoVo));
        String sign =WxPayUtil.getSign(vo, signKey);
        vo.setSign(sign);
        return vo;
    }
}
